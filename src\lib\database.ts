import Dexie, { type EntityTable } from 'dexie';
import { v4 as uuidv4 } from 'uuid';
import type {
  Task,
  Project,
  Note,
  User,
  Tag,
  Attachment,
  TimeEntry,
  Comment,
  Reminder,
  SearchIndex,
  ProductivityMetric,
  Goal,
  ActivityLog,
  BaseEntity
} from '@/types/database';

// Database class extending Dexie
export class ZenithDatabase extends Dexie {
  // Entity tables
  tasks!: EntityTable<Task, 'id'>;
  projects!: EntityTable<Project, 'id'>;
  notes!: EntityTable<Note, 'id'>;
  users!: EntityTable<User, 'id'>;
  tags!: EntityTable<Tag, 'id'>;
  attachments!: EntityTable<Attachment, 'id'>;
  timeEntries!: EntityTable<TimeEntry, 'id'>;
  comments!: EntityTable<Comment, 'id'>;
  reminders!: EntityTable<Reminder, 'id'>;
  searchIndex!: EntityTable<SearchIndex, 'id'>;
  productivityMetrics!: EntityTable<ProductivityMetric, 'id'>;
  goals!: EntityTable<Goal, 'id'>;
  activityLogs!: EntityTable<ActivityLog, 'id'>;

  constructor() {
    super('ZenithPulseManager');

    // Define database schema
    this.version(2).stores({
      tasks: '++id, title, status, priority, dueDate, projectId, assignedTo, createdAt, updatedAt, order, *tags',
      projects: '++id, name, status, priority, startDate, endDate, deadline, createdAt, updatedAt, *teamMembers, *tags',
      notes: '++id, title, isPinned, isArchived, projectId, taskId, createdAt, updatedAt, lastViewedAt, *tags',
      users: '++id, name, email, role, isActive, createdAt, updatedAt',
      tags: '++id, name, category, usageCount, createdAt, updatedAt',
      attachments: '++id, name, type, entityType, entityId, uploadedBy, createdAt, updatedAt',
      timeEntries: '++id, taskId, projectId, userId, startTime, endTime, duration, billable, createdAt, updatedAt',
      comments: '++id, entityType, entityId, userId, parentCommentId, createdAt, updatedAt, *mentions',
      reminders: '++id, reminderTime, isCompleted, entityType, entityId, userId, createdAt, updatedAt',
      searchIndex: '++id, entityType, entityId, title, searchableText, lastIndexed',
      productivityMetrics: '++id, userId, date, tasksCompleted, hoursWorked, productivityScore, createdAt, updatedAt',
      goals: '++id, userId, deadline, isCompleted, category, createdAt, updatedAt',
      activityLogs: '++id, userId, action, entityType, entityId, createdAt'
    });

    // Add hooks for automatic timestamp management
    this.tasks.hook('creating', this.addTimestamps);
    this.tasks.hook('updating', this.updateTimestamp);
    
    this.projects.hook('creating', this.addTimestamps);
    this.projects.hook('updating', this.updateTimestamp);
    
    this.notes.hook('creating', this.addTimestamps);
    this.notes.hook('updating', this.updateTimestamp);
    
    this.users.hook('creating', this.addTimestamps);
    this.users.hook('updating', this.updateTimestamp);
    
    this.tags.hook('creating', this.addTimestamps);
    this.tags.hook('updating', this.updateTimestamp);
    
    this.attachments.hook('creating', this.addTimestamps);
    this.attachments.hook('updating', this.updateTimestamp);
    
    this.timeEntries.hook('creating', this.addTimestamps);
    this.timeEntries.hook('updating', this.updateTimestamp);
    
    this.comments.hook('creating', this.addTimestamps);
    this.comments.hook('updating', this.updateTimestamp);
    
    this.reminders.hook('creating', this.addTimestamps);
    this.reminders.hook('updating', this.updateTimestamp);
    
    this.productivityMetrics.hook('creating', this.addTimestamps);
    this.productivityMetrics.hook('updating', this.updateTimestamp);
    
    this.goals.hook('creating', this.addTimestamps);
    this.goals.hook('updating', this.updateTimestamp);
    
    this.activityLogs.hook('creating', this.addTimestamps);
  }

  // Hook functions for automatic timestamp management
  private addTimestamps = (primKey: string | number, obj: BaseEntity, trans: unknown) => {
    const now = new Date();
    if (!obj.id) {
      obj.id = uuidv4();
    }
    obj.createdAt = now;
    obj.updatedAt = now;
  };

  private updateTimestamp = (modifications: Partial<BaseEntity>, primKey: string | number, obj: BaseEntity, trans: unknown) => {
    modifications.updatedAt = new Date();
  };

  // Utility methods for common operations
  async clearAllData(): Promise<void> {
    await this.transaction('rw', this.tables, async () => {
      await Promise.all(this.tables.map(table => table.clear()));
    });
  }

  async exportData(): Promise<Record<string, unknown>> {
    const data = {
      version: '1.0.0',
      exportDate: new Date(),
      tasks: await this.tasks.toArray(),
      projects: await this.projects.toArray(),
      notes: await this.notes.toArray(),
      users: await this.users.toArray(),
      tags: await this.tags.toArray(),
      timeEntries: await this.timeEntries.toArray(),
      goals: await this.goals.toArray(),
      attachments: await this.attachments.toArray(),
      comments: await this.comments.toArray(),
      reminders: await this.reminders.toArray(),
      productivityMetrics: await this.productivityMetrics.toArray(),
      activityLogs: await this.activityLogs.toArray()
    };
    return data;
  }

  async importData(data: Record<string, unknown>, options: { overwrite?: boolean } = {}): Promise<void> {
    await this.transaction('rw', this.tables, async () => {
      if (options.overwrite) {
        await this.clearAllData();
      }

      // Import data in dependency order
      if (data.users) await this.users.bulkPut(data.users);
      if (data.tags) await this.tags.bulkPut(data.tags);
      if (data.projects) await this.projects.bulkPut(data.projects);
      if (data.tasks) await this.tasks.bulkPut(data.tasks);
      if (data.notes) await this.notes.bulkPut(data.notes);
      if (data.timeEntries) await this.timeEntries.bulkPut(data.timeEntries);
      if (data.goals) await this.goals.bulkPut(data.goals);
      if (data.attachments) await this.attachments.bulkPut(data.attachments);
      if (data.comments) await this.comments.bulkPut(data.comments);
      if (data.reminders) await this.reminders.bulkPut(data.reminders);
      if (data.productivityMetrics) await this.productivityMetrics.bulkPut(data.productivityMetrics);
      if (data.activityLogs) await this.activityLogs.bulkPut(data.activityLogs);
    });
  }

  // Search functionality
  async searchEntities(query: string, entityTypes: string[] = ['task', 'project', 'note']): Promise<SearchIndex[]> {
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
    
    const results = await this.searchIndex
      .where('entityType')
      .anyOf(entityTypes)
      .and(item => {
        const searchText = item.searchableText.toLowerCase();
        return searchTerms.every(term => searchText.includes(term));
      })
      .toArray();

    return results;
  }

  // Update search index for an entity
  async updateSearchIndex(entityType: string, entityId: string, title: string, content: string, tags: string[] = []): Promise<void> {
    const searchableText = [title, content, ...tags].join(' ').toLowerCase();
    
    await this.searchIndex.put({
      id: `${entityType}_${entityId}`,
      entityType,
      entityId,
      title,
      content,
      tags,
      searchableText,
      lastIndexed: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  // Activity logging
  async logActivity(userId: string, action: string, entityType: string, entityId: string, details: Record<string, unknown> = {}): Promise<void> {
    await this.activityLogs.add({
      id: uuidv4(),
      userId,
      action,
      entityType,
      entityId,
      details,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  // Database health check
  async healthCheck(): Promise<{ status: 'healthy' | 'error', details: Record<string, unknown> }> {
    try {
      const counts = {
        tasks: await this.tasks.count(),
        projects: await this.projects.count(),
        notes: await this.notes.count(),
        users: await this.users.count(),
        tags: await this.tags.count()
      };

      return {
        status: 'healthy',
        details: {
          isOpen: this.isOpen(),
          version: this.verno,
          counts,
          lastCheck: new Date()
        }
      };
    } catch (error) {
      return {
        status: 'error',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          lastCheck: new Date()
        }
      };
    }
  }
}

// Create and export database instance
export const db = new ZenithDatabase();

// Initialize database with default data if empty
export async function initializeDatabase(): Promise<void> {
  try {
    // First try to open the database
    try {
      await db.open();
    } catch (error) {
      // If there's a schema error, delete and recreate the database
      if (error instanceof Error && (error.message.includes('primary key') || error.message.includes('Schema'))) {
        console.log('Database schema incompatible, recreating database...');
        await db.delete();
        await db.open();
      } else {
        throw error;
      }
    }

    // Check if database is empty and needs initialization
    const userCount = await db.users.count();

    if (userCount === 0) {
      // Create default user
      const defaultUser: User = {
        id: uuidv4(),
        name: 'Default User',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        preferences: {
          language: 'en',
          theme: 'system',
          timezone: 'UTC',
          dateFormat: 'YYYY-MM-DD',
          timeFormat: '24h',
          notifications: {
            email: true,
            push: true,
            taskReminders: true,
            projectUpdates: true,
            deadlineAlerts: true,
            reminderTime: 15
          },
          dashboard: {
            widgets: ['tasks', 'projects', 'productivity', 'notes'],
            layout: 'grid',
            showCompletedTasks: false,
            defaultView: 'dashboard'
          }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.users.add(defaultUser);

      // Create some default tags
      const defaultTags: Tag[] = [
        { id: uuidv4(), name: 'urgent', color: '#ef4444', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
        { id: uuidv4(), name: 'work', color: '#3b82f6', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
        { id: uuidv4(), name: 'personal', color: '#10b981', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
        { id: uuidv4(), name: 'meeting', color: '#8b5cf6', category: 'task', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
        { id: uuidv4(), name: 'development', color: '#f59e0b', category: 'project', usageCount: 0, createdAt: new Date(), updatedAt: new Date() }
      ];

      await db.tags.bulkAdd(defaultTags);

      console.log('Database initialized with default data');
    }
  } catch (error) {
    console.error('Failed to initialize database:', error);

    // If it's a schema error, try to delete and recreate the database
    if (error instanceof Error && error.message.includes('Schema')) {
      console.log('Schema error detected, attempting to reset database...');
      try {
        await db.delete();
        await db.open();

        // Retry initialization
        const userCount = await db.users.count();
        if (userCount === 0) {
          // Create default user again
          const defaultUser: User = {
            id: uuidv4(),
            name: 'Default User',
            email: '<EMAIL>',
            role: 'admin',
            isActive: true,
            preferences: {
              language: 'en',
              theme: 'system',
              timezone: 'UTC',
              dateFormat: 'YYYY-MM-DD',
              timeFormat: '24h',
              notifications: {
                email: true,
                push: true,
                taskReminders: true,
                projectUpdates: true,
                deadlineAlerts: true,
                reminderTime: 15
              },
              dashboard: {
                widgets: ['tasks', 'projects', 'productivity', 'notes'],
                layout: 'grid',
                showCompletedTasks: false,
                defaultView: 'dashboard'
              }
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await db.users.add(defaultUser);

          // Create default tags
          const defaultTags: Tag[] = [
            { id: uuidv4(), name: 'urgent', color: '#ef4444', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
            { id: uuidv4(), name: 'work', color: '#3b82f6', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
            { id: uuidv4(), name: 'personal', color: '#10b981', category: 'general', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
            { id: uuidv4(), name: 'meeting', color: '#8b5cf6', category: 'task', usageCount: 0, createdAt: new Date(), updatedAt: new Date() },
            { id: uuidv4(), name: 'development', color: '#f59e0b', category: 'project', usageCount: 0, createdAt: new Date(), updatedAt: new Date() }
          ];

          await db.tags.bulkAdd(defaultTags);

          console.log('Database reset and reinitialized successfully');
        }
      } catch (resetError) {
        console.error('Failed to reset database:', resetError);
        throw resetError;
      }
    } else {
      throw error;
    }
  }
}

// Function to reset database (useful for development or troubleshooting)
export async function resetDatabase(): Promise<void> {
  try {
    await db.delete();
    console.log('Database deleted successfully');
    await initializeDatabase();
    console.log('Database recreated successfully');
  } catch (error) {
    console.error('Failed to reset database:', error);
    throw error;
  }
}

export default db;
