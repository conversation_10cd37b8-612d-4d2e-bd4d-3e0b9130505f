import { db } from './database';
import { taskService } from '@/services/taskService';
import { projectService } from '@/services/projectService';
import { noteService } from '@/services/noteService';
import type { Task, Project, Note } from '@/types/database';

export async function seedDatabase(): Promise<void> {
  try {
    // Check if data already exists
    const existingTasks = await db.tasks.count();
    if (existingTasks > 0) {
      console.log('Database already has data, skipping seed');
      return;
    }

    console.log('Seeding database with sample data...');

    // Create sample projects
    const projects = await Promise.all([
      projectService.createProject({
        name: 'Mobile App Development',
        description: 'Developing a new mobile application for iOS and Android platforms',
        status: 'active',
        progress: 65,
        color: '#3b82f6',
        teamMembers: ['user1', 'user2', 'user3'],
        tags: ['mobile', 'development', 'ios', 'android'],
        priority: 'high',
        startDate: new Date('2024-01-01'),
        deadline: new Date('2024-06-30'),
        budget: 50000
      }),
      
      projectService.createProject({
        name: 'Website Redesign',
        description: 'Complete redesign of the company website with modern UI/UX',
        status: 'active',
        progress: 40,
        color: '#10b981',
        teamMembers: ['user1', 'user4'],
        tags: ['web', 'design', 'ui', 'ux'],
        priority: 'medium',
        startDate: new Date('2024-02-01'),
        deadline: new Date('2024-05-15'),
        budget: 25000
      }),
      
      projectService.createProject({
        name: 'Data Analytics Platform',
        description: 'Building a comprehensive data analytics and reporting platform',
        status: 'planning',
        progress: 15,
        color: '#8b5cf6',
        teamMembers: ['user2', 'user5'],
        tags: ['analytics', 'data', 'reporting'],
        priority: 'high',
        startDate: new Date('2024-03-01'),
        deadline: new Date('2024-12-31'),
        budget: 75000
      })
    ]);

    // Create sample tasks
    const tasks = await Promise.all([
      // Tasks for Mobile App Development
      taskService.createTask({
        title: 'Design user authentication flow',
        description: 'Create wireframes and mockups for the user login and registration process',
        priority: 'high',
        status: 'completed',
        projectId: projects[0].id,
        tags: ['design', 'auth', 'ui'],
        dueDate: new Date('2024-01-15'),
        completedAt: new Date('2024-01-14'),
        estimatedHours: 8,
        actualHours: 6,
        order: 1
      }),
      
      taskService.createTask({
        title: 'Implement user registration API',
        description: 'Develop backend API endpoints for user registration and validation',
        priority: 'high',
        status: 'inProgress',
        projectId: projects[0].id,
        tags: ['backend', 'api', 'auth'],
        dueDate: new Date('2024-01-20'),
        estimatedHours: 12,
        order: 2
      }),
      
      taskService.createTask({
        title: 'Set up push notifications',
        description: 'Configure push notification service for both iOS and Android',
        priority: 'medium',
        status: 'todo',
        projectId: projects[0].id,
        tags: ['notifications', 'mobile'],
        dueDate: new Date('2024-01-25'),
        estimatedHours: 16,
        order: 3
      }),
      
      // Tasks for Website Redesign
      taskService.createTask({
        title: 'Create new homepage design',
        description: 'Design modern and responsive homepage layout',
        priority: 'high',
        status: 'completed',
        projectId: projects[1].id,
        tags: ['design', 'homepage', 'responsive'],
        dueDate: new Date('2024-02-10'),
        completedAt: new Date('2024-02-08'),
        estimatedHours: 20,
        actualHours: 18,
        order: 1
      }),
      
      taskService.createTask({
        title: 'Optimize website performance',
        description: 'Improve page load times and overall website performance',
        priority: 'medium',
        status: 'inProgress',
        projectId: projects[1].id,
        tags: ['performance', 'optimization'],
        dueDate: new Date('2024-02-20'),
        estimatedHours: 15,
        order: 2
      }),
      
      // General tasks
      taskService.createTask({
        title: 'Weekly team meeting',
        description: 'Discuss project progress and upcoming milestones',
        priority: 'low',
        status: 'todo',
        tags: ['meeting', 'team'],
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        estimatedHours: 2,
        order: 1
      }),
      
      taskService.createTask({
        title: 'Review code submissions',
        description: 'Review and approve pending code submissions from team members',
        priority: 'medium',
        status: 'todo',
        tags: ['review', 'code'],
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
        estimatedHours: 4,
        order: 2
      })
    ]);

    // Create sample notes
    const notes = await Promise.all([
      noteService.createNote({
        title: 'Project Planning Ideas',
        content: `# Project Planning Ideas

## Mobile App Features
- User authentication with social login
- Real-time notifications
- Offline data synchronization
- Dark mode support
- Multi-language support

## Technical Considerations
- Use React Native for cross-platform development
- Implement Redux for state management
- Use Firebase for backend services
- Consider implementing CI/CD pipeline

## Next Steps
1. Finalize feature requirements
2. Create detailed technical specifications
3. Set up development environment
4. Begin prototype development`,
        tags: ['planning', 'mobile', 'ideas'],
        isPinned: true,
        isArchived: false,
        projectId: projects[0].id,
        attachments: []
      }),
      
      noteService.createNote({
        title: 'Meeting Notes - Jan 15, 2024',
        content: `# Team Meeting Notes

**Date:** January 15, 2024
**Attendees:** John, Sarah, Mike, Lisa

## Agenda Items Discussed

### 1. Project Status Updates
- Mobile app development is 65% complete
- Website redesign on track for Q2 delivery
- Data analytics platform planning phase started

### 2. Upcoming Deadlines
- User registration API due Jan 20
- Homepage design review Jan 22
- Sprint planning meeting Jan 25

### 3. Action Items
- [ ] John to review API documentation
- [ ] Sarah to prepare design mockups
- [ ] Mike to set up testing environment
- [ ] Lisa to update project timeline

### 4. Blockers and Issues
- Waiting for client feedback on design proposals
- Need additional resources for analytics platform
- Server migration scheduled for next week`,
        tags: ['meeting', 'notes', 'team'],
        isPinned: false,
        isArchived: false,
        attachments: []
      }),
      
      noteService.createNote({
        title: 'Design System Guidelines',
        content: `# Design System Guidelines

## Color Palette
- Primary: #3b82f6 (Blue)
- Secondary: #10b981 (Green)
- Accent: #8b5cf6 (Purple)
- Warning: #f59e0b (Orange)
- Error: #ef4444 (Red)

## Typography
- Headings: Inter, sans-serif
- Body: Inter, sans-serif
- Code: JetBrains Mono, monospace

## Spacing Scale
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px

## Component Guidelines
- Use consistent border radius (8px for cards, 4px for buttons)
- Maintain 4:1 contrast ratio for accessibility
- Implement hover and focus states for all interactive elements
- Use semantic HTML elements where possible`,
        tags: ['design', 'guidelines', 'system'],
        isPinned: true,
        isArchived: false,
        projectId: projects[1].id,
        attachments: []
      }),
      
      noteService.createNote({
        title: 'Learning Resources',
        content: `# Learning Resources

## React & TypeScript
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

## Design & UX
- [Material Design Guidelines](https://material.io/design)
- [Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)
- [Figma Academy](https://www.figma.com/academy/)

## Development Tools
- [Vite Documentation](https://vitejs.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui Components](https://ui.shadcn.com/)

## Best Practices
- [Clean Code principles](https://github.com/ryanmcdermott/clean-code-javascript)
- [Git workflow best practices](https://www.atlassian.com/git/tutorials/comparing-workflows)
- [API design guidelines](https://github.com/microsoft/api-guidelines)`,
        tags: ['learning', 'resources', 'development'],
        isPinned: false,
        isArchived: false,
        attachments: []
      })
    ]);

    console.log('Database seeded successfully!');
    console.log(`Created ${projects.length} projects, ${tasks.length} tasks, and ${notes.length} notes`);
    
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

// Function to clear all data (useful for development)
export async function clearDatabase(): Promise<void> {
  try {
    await db.clearAllData();
    console.log('Database cleared successfully');
  } catch (error) {
    console.error('Error clearing database:', error);
    throw error;
  }
}
