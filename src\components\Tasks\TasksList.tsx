
import React, { useState, useMemo } from 'react';
import { CheckSquare, Plus, Filter, Search, Loader2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useTasks, useCompleteTask, useUpdateTask } from '@/hooks/useData';
import type { TaskFilter } from '@/types/database';

const TasksList: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Build filter object for the query
  const filter: TaskFilter = useMemo(() => {
    const baseFilter: TaskFilter = {};

    if (statusFilter !== 'all') {
      baseFilter.status = [statusFilter as 'todo' | 'inProgress' | 'completed' | 'cancelled'];
    }

    return baseFilter;
  }, [statusFilter]);

  const { data: tasksResult, isLoading, error } = useTasks(filter, { page: 1, limit: 100, sortBy: 'updatedAt', sortOrder: 'desc' });
  const completeTaskMutation = useCompleteTask();
  const updateTaskMutation = useUpdateTask();

  // Filter tasks by search query locally
  const filteredTasks = useMemo(() => {
    if (!tasksResult?.data) return [];

    if (!searchQuery.trim()) return tasksResult.data;

    const query = searchQuery.toLowerCase();
    return tasksResult.data.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query) ||
      task.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }, [tasksResult?.data, searchQuery]);

  const handleToggleComplete = async (taskId: string, currentStatus: string) => {
    if (currentStatus !== 'completed') {
      try {
        await completeTaskMutation.mutateAsync(taskId);
      } catch (error) {
        console.error('Failed to complete task:', error);
      }
    } else {
      try {
        await updateTaskMutation.mutateAsync({
          id: taskId,
          updates: { status: 'todo' }
        });
      } catch (error) {
        console.error('Failed to update task:', error);
      }
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat(isRTL ? 'ar' : 'en', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-muted rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('tasks.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('tasks.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('tasks.newTask')}
        </button>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <input
            type="text"
            placeholder={t('common.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        {/* Status Filters */}
        <div className={cn(
          "flex items-center gap-4",
          isRTL && "flex-row-reverse"
        )}>
          <div className="flex gap-2">
            {['all', 'todo', 'inProgress', 'completed'].map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                  statusFilter === status
                    ? "bg-zenith-gradient text-white shadow-zenith"
                    : "bg-card hover:bg-accent text-foreground border border-border"
                )}
              >
                {t(`tasks.status.${status}`) || status}
              </button>
            ))}
          </div>

          <div className="flex gap-2 ml-auto">
            <button className="p-2 bg-card hover:bg-accent text-muted-foreground rounded-lg transition-colors border border-border">
              <Filter className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Tasks Grid */}
      <div className="grid gap-4">
        {filteredTasks.length === 0 ? (
          <div className="text-center py-12">
            <CheckSquare className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">{t('tasks.noTasks')}</h3>
            <p className="text-muted-foreground">{t('tasks.noTasksDescription')}</p>
          </div>
        ) : (
          filteredTasks.map((task) => {
            const isCompleted = task.status === 'completed';

            return (
              <div
                key={task.id}
                className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200"
              >
                <div className={cn(
                  "flex items-start gap-4",
                  isRTL && "flex-row-reverse"
                )}>
                  <button
                    onClick={() => handleToggleComplete(task.id, task.status)}
                    disabled={completeTaskMutation.isPending || updateTaskMutation.isPending}
                    className={cn(
                      "flex-shrink-0 w-6 h-6 rounded border-2 transition-colors flex items-center justify-center",
                      isCompleted
                        ? "bg-blue-500 border-blue-500 text-white"
                        : "border-muted-foreground hover:border-blue-400",
                      (completeTaskMutation.isPending || updateTaskMutation.isPending) && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    {isCompleted && <CheckSquare className="w-4 h-4" />}
                  </button>

                  <div className={cn("flex-1", isRTL && "text-right")}>
                    <h3 className={cn(
                      "text-lg font-semibold mb-2",
                      isCompleted && "line-through text-muted-foreground"
                    )}>
                      {task.title}
                    </h3>
                    {task.description && (
                      <p className="text-muted-foreground mb-4">{task.description}</p>
                    )}

                    <div className={cn(
                      "flex items-center gap-4 text-sm flex-wrap",
                      isRTL && "flex-row-reverse"
                    )}>
                      <span className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        task.priority === 'high' ? priority.high.className :
                        task.priority === 'medium' ? priority.medium.className :
                        priority.low.className
                      )}>
                        {t(`tasks.priority.${task.priority}`)}
                      </span>

                      {task.dueDate && (
                        <span className="text-muted-foreground">
                          {t('tasks.dueDate')}: {formatDate(task.dueDate)}
                        </span>
                      )}

                      {task.tags.length > 0 && (
                        <div className="flex gap-1 flex-wrap">
                          {task.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default TasksList;
