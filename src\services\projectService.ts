import { db } from '@/lib/database';
import type { Project, ProjectFilter, PaginationOptions, PaginatedResult, Task } from '@/types/database';
import { v4 as uuidv4 } from 'uuid';

export class ProjectService {
  // Create a new project
  async createProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
    try {
      const project: Project = {
        ...projectData,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.projects.add(project);
      
      // Update search index
      await db.updateSearchIndex(
        'project',
        project.id,
        project.name,
        project.description || '',
        project.tags
      );

      // Log activity
      await db.logActivity(
        'current-user',
        'create',
        'project',
        project.id,
        { name: project.name, status: project.status }
      );

      return project;
    } catch (error) {
      console.error('Error creating project:', error);
      throw new Error('Failed to create project');
    }
  }

  // Get project by ID
  async getProjectById(id: string): Promise<Project | undefined> {
    try {
      return await db.projects.get(id);
    } catch (error) {
      console.error('Error getting project:', error);
      throw new Error('Failed to get project');
    }
  }

  // Get all projects with optional filtering and pagination
  async getProjects(
    filter: ProjectFilter = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): Promise<PaginatedResult<Project>> {
    try {
      let query = db.projects.orderBy(pagination.sortBy || 'createdAt');

      // Apply sorting
      if (pagination.sortOrder === 'desc') {
        query = query.reverse();
      }

      // Apply filters
      let collection = query.toCollection();

      if (filter.status && filter.status.length > 0) {
        collection = collection.filter(project => filter.status!.includes(project.status));
      }

      if (filter.priority && filter.priority.length > 0) {
        collection = collection.filter(project => filter.priority!.includes(project.priority));
      }

      if (filter.teamMember) {
        collection = collection.filter(project => 
          project.teamMembers.includes(filter.teamMember!)
        );
      }

      if (filter.tags && filter.tags.length > 0) {
        collection = collection.filter(project => 
          filter.tags!.some(tag => project.tags.includes(tag))
        );
      }

      if (filter.startDateFrom) {
        collection = collection.filter(project => 
          project.startDate && project.startDate >= filter.startDateFrom!
        );
      }

      if (filter.startDateTo) {
        collection = collection.filter(project => 
          project.startDate && project.startDate <= filter.startDateTo!
        );
      }

      if (filter.endDateFrom) {
        collection = collection.filter(project => 
          project.endDate && project.endDate >= filter.endDateFrom!
        );
      }

      if (filter.endDateTo) {
        collection = collection.filter(project => 
          project.endDate && project.endDate <= filter.endDateTo!
        );
      }

      // Get total count
      const total = await collection.count();

      // Apply pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const data = await collection.offset(offset).limit(pagination.limit).toArray();

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        data,
        total,
        page: pagination.page,
        limit: pagination.limit,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1
      };
    } catch (error) {
      console.error('Error getting projects:', error);
      throw new Error('Failed to get projects');
    }
  }

  // Update project
  async updateProject(id: string, updates: Partial<Omit<Project, 'id' | 'createdAt'>>): Promise<Project> {
    try {
      const existingProject = await db.projects.get(id);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      const updatedProject = {
        ...existingProject,
        ...updates,
        updatedAt: new Date()
      };

      await db.projects.update(id, updatedProject);

      // Update search index if name, description, or tags changed
      if (updates.name || updates.description || updates.tags) {
        await db.updateSearchIndex(
          'project',
          id,
          updatedProject.name,
          updatedProject.description || '',
          updatedProject.tags
        );
      }

      // Log activity
      await db.logActivity(
        'current-user',
        'update',
        'project',
        id,
        { changes: updates }
      );

      return updatedProject;
    } catch (error) {
      console.error('Error updating project:', error);
      throw new Error('Failed to update project');
    }
  }

  // Delete project
  async deleteProject(id: string): Promise<void> {
    try {
      const project = await db.projects.get(id);
      if (!project) {
        throw new Error('Project not found');
      }

      await db.transaction('rw', [db.projects, db.tasks, db.searchIndex, db.comments, db.timeEntries], async () => {
        // Delete project
        await db.projects.delete(id);
        
        // Delete from search index
        await db.searchIndex.where('entityId').equals(id).delete();
        
        // Delete related comments
        await db.comments.where('entityId').equals(id).delete();
        
        // Update tasks to remove project reference
        await db.tasks.where('projectId').equals(id).modify({ projectId: undefined });
        
        // Delete related time entries
        await db.timeEntries.where('projectId').equals(id).delete();
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'delete',
        'project',
        id,
        { name: project.name }
      );
    } catch (error) {
      console.error('Error deleting project:', error);
      throw new Error('Failed to delete project');
    }
  }

  // Get active projects
  async getActiveProjects(): Promise<Project[]> {
    try {
      return await db.projects
        .where('status')
        .equals('active')
        .toArray();
    } catch (error) {
      console.error('Error getting active projects:', error);
      throw new Error('Failed to get active projects');
    }
  }

  // Get project statistics
  async getProjectStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    planning: number;
    onHold: number;
    cancelled: number;
  }> {
    try {
      const [total, active, completed, planning, onHold, cancelled] = await Promise.all([
        db.projects.count(),
        db.projects.where('status').equals('active').count(),
        db.projects.where('status').equals('completed').count(),
        db.projects.where('status').equals('planning').count(),
        db.projects.where('status').equals('onHold').count(),
        db.projects.where('status').equals('cancelled').count()
      ]);

      return {
        total,
        active,
        completed,
        planning,
        onHold,
        cancelled
      };
    } catch (error) {
      console.error('Error getting project stats:', error);
      throw new Error('Failed to get project stats');
    }
  }

  // Get project with tasks
  async getProjectWithTasks(id: string): Promise<{
    project: Project;
    tasks: Task[];
    taskStats: {
      total: number;
      completed: number;
      inProgress: number;
      todo: number;
    };
  } | null> {
    try {
      const project = await db.projects.get(id);
      if (!project) {
        return null;
      }

      const tasks = await db.tasks.where('projectId').equals(id).toArray();
      
      const taskStats = {
        total: tasks.length,
        completed: tasks.filter(t => t.status === 'completed').length,
        inProgress: tasks.filter(t => t.status === 'inProgress').length,
        todo: tasks.filter(t => t.status === 'todo').length
      };

      return {
        project,
        tasks,
        taskStats
      };
    } catch (error) {
      console.error('Error getting project with tasks:', error);
      throw new Error('Failed to get project with tasks');
    }
  }

  // Update project progress based on tasks
  async updateProjectProgress(id: string): Promise<Project> {
    try {
      const tasks = await db.tasks.where('projectId').equals(id).toArray();
      
      if (tasks.length === 0) {
        return await this.updateProject(id, { progress: 0 });
      }

      const completedTasks = tasks.filter(task => task.status === 'completed').length;
      const progress = Math.round((completedTasks / tasks.length) * 100);

      return await this.updateProject(id, { progress });
    } catch (error) {
      console.error('Error updating project progress:', error);
      throw new Error('Failed to update project progress');
    }
  }

  // Get projects by team member
  async getProjectsByTeamMember(userId: string): Promise<Project[]> {
    try {
      return await db.projects
        .filter(project => project.teamMembers.includes(userId))
        .toArray();
    } catch (error) {
      console.error('Error getting projects by team member:', error);
      throw new Error('Failed to get projects by team member');
    }
  }

  // Get overdue projects
  async getOverdueProjects(): Promise<Project[]> {
    try {
      const now = new Date();
      return await db.projects
        .where('deadline')
        .below(now)
        .and(project => project.status !== 'completed' && project.status !== 'cancelled')
        .toArray();
    } catch (error) {
      console.error('Error getting overdue projects:', error);
      throw new Error('Failed to get overdue projects');
    }
  }

  // Search projects
  async searchProjects(query: string): Promise<Project[]> {
    try {
      const searchResults = await db.searchEntities(query, ['project']);
      const projectIds = searchResults.map(result => result.entityId);
      
      if (projectIds.length === 0) {
        return [];
      }

      return await db.projects.where('id').anyOf(projectIds).toArray();
    } catch (error) {
      console.error('Error searching projects:', error);
      throw new Error('Failed to search projects');
    }
  }

  // Archive project
  async archiveProject(id: string): Promise<Project> {
    try {
      return await this.updateProject(id, { status: 'completed' });
    } catch (error) {
      console.error('Error archiving project:', error);
      throw new Error('Failed to archive project');
    }
  }

  // Duplicate project
  async duplicateProject(id: string, newName?: string): Promise<Project> {
    try {
      const originalProject = await db.projects.get(id);
      if (!originalProject) {
        throw new Error('Project not found');
      }

      const duplicatedProject: Project = {
        ...originalProject,
        id: uuidv4(),
        name: newName || `${originalProject.name} (Copy)`,
        status: 'planning',
        progress: 0,
        startDate: undefined,
        endDate: undefined,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.projects.add(duplicatedProject);

      // Update search index
      await db.updateSearchIndex(
        'project',
        duplicatedProject.id,
        duplicatedProject.name,
        duplicatedProject.description || '',
        duplicatedProject.tags
      );

      // Log activity
      await db.logActivity(
        'current-user',
        'duplicate',
        'project',
        duplicatedProject.id,
        { originalId: id, name: duplicatedProject.name }
      );

      return duplicatedProject;
    } catch (error) {
      console.error('Error duplicating project:', error);
      throw new Error('Failed to duplicate project');
    }
  }
}

// Export singleton instance
export const projectService = new ProjectService();
