import { useTheme as useNextTheme } from 'next-themes';
import { useMemo, useCallback } from 'react';
import { componentThemes } from '@/lib/component-themes';

/**
 * Enhanced theme hook that provides access to both next-themes functionality
 * and our component theme system with OKLCH color support
 */
export const useTheme = () => {
  const { theme, setTheme, resolvedTheme, systemTheme } = useNextTheme();

  // Get component themes with current theme context
  const themes = useMemo(() => componentThemes, []);

  // Helper function to get theme-aware styles
  const getThemeStyles = useMemo(() => {
    return {
      // Get button styles with variant
      button: (variant: keyof typeof componentThemes.button.variants = 'primary', size: keyof typeof componentThemes.button.sizes = 'md') => ({
        ...componentThemes.button.base,
        ...componentThemes.button.variants[variant],
        ...componentThemes.button.sizes[size],
      }),

      // Get card styles with variant
      card: (variant: keyof typeof componentThemes.card.variants = 'default') => ({
        ...componentThemes.card.base,
        ...componentThemes.card.variants[variant],
      }),

      // Get input styles with state
      input: (state: keyof typeof componentThemes.input.states = 'default') => ({
        ...componentThemes.input.base,
        ...componentThemes.input.states[state],
      }),

      // Get badge styles with variant
      badge: (variant: keyof typeof componentThemes.badge.variants = 'default') => ({
        ...componentThemes.badge.base,
        ...componentThemes.badge.variants[variant],
      }),

      // Get navigation styles
      navigation: {
        container: componentThemes.navigation.base,
        item: (state: keyof typeof componentThemes.navigation.item.states = 'default') => ({
          ...componentThemes.navigation.item.base,
          ...componentThemes.navigation.item.states[state],
        }),
      },

      // Get modal styles
      modal: {
        overlay: componentThemes.modal.overlay,
        content: componentThemes.modal.content,
      },

      // Get tooltip styles
      tooltip: componentThemes.tooltip.base,
    };
  }, []);

  // Helper function to check if current theme is dark
  const isDark = useMemo(() => {
    return resolvedTheme === 'dark';
  }, [resolvedTheme]);

  // Helper function to get CSS custom property value
  const getCSSVariable = (property: string) => {
    if (typeof window !== 'undefined') {
      return getComputedStyle(document.documentElement).getPropertyValue(property);
    }
    return '';
  };

  // Helper function to get OKLCH color values
  const getOKLCHColor = useCallback((cssVar: string) => {
    const value = getCSSVariable(cssVar);
    return value ? `oklch(${value})` : '';
  }, [getCSSVariable]);

  // Theme-aware color helpers
  const colors = useMemo(() => ({
    background: getOKLCHColor('--background'),
    foreground: getOKLCHColor('--foreground'),
    card: getOKLCHColor('--card'),
    cardForeground: getOKLCHColor('--card-foreground'),
    popover: getOKLCHColor('--popover'),
    popoverForeground: getOKLCHColor('--popover-foreground'),
    primary: getOKLCHColor('--primary'),
    primaryForeground: getOKLCHColor('--primary-foreground'),
    secondary: getOKLCHColor('--secondary'),
    secondaryForeground: getOKLCHColor('--secondary-foreground'),
    muted: getOKLCHColor('--muted'),
    mutedForeground: getOKLCHColor('--muted-foreground'),
    accent: getOKLCHColor('--accent'),
    accentForeground: getOKLCHColor('--accent-foreground'),
    destructive: getOKLCHColor('--destructive'),
    destructiveForeground: getOKLCHColor('--destructive-foreground'),
    border: getOKLCHColor('--border'),
    input: getOKLCHColor('--input'),
    ring: getOKLCHColor('--ring'),
  }), [getOKLCHColor]);

  return {
    // Next-themes functionality
    theme,
    setTheme,
    resolvedTheme,
    systemTheme,
    isDark,

    // Component themes
    themes,
    getThemeStyles,

    // Color helpers
    colors,
    getCSSVariable,
    getOKLCHColor,

    // Utility functions
    toggleTheme: () => {
      setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
    },

    // Theme class helpers for conditional styling
    themeClasses: {
      light: resolvedTheme === 'light',
      dark: resolvedTheme === 'dark',
      system: theme === 'system',
    },
  };
};

export default useTheme;
