
import React, { useState, useMemo } from 'react';
import { FileText, Plus, Search, Filter, Pin, Archive, StickyNote } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useNotes, useTogglePinNote } from '@/hooks/useData';
import type { NoteFilter } from '@/types/database';

const NotesGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [showArchived, setShowArchived] = useState(false);

  // Build filter object for the query
  const filter: NoteFilter = useMemo(() => {
    const baseFilter: NoteFilter = {
      isArchived: showArchived
    };

    return baseFilter;
  }, [showArchived]);

  const { data: notesResult, isLoading, error } = useNotes(filter, {
    page: 1,
    limit: 100,
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  });

  const togglePinMutation = useTogglePinNote();

  // Filter notes by search query locally
  const filteredNotes = useMemo(() => {
    if (!notesResult?.data) return [];

    if (!searchQuery.trim()) return notesResult.data;

    const query = searchQuery.toLowerCase();
    return notesResult.data.filter(note =>
      note.title.toLowerCase().includes(query) ||
      note.content.toLowerCase().includes(query) ||
      note.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }, [notesResult?.data, searchQuery]);

  const handleTogglePin = async (noteId: string) => {
    try {
      await togglePinMutation.mutateAsync(noteId);
    } catch (error) {
      console.error('Failed to toggle pin note:', error);
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat(isRTL ? 'ar' : 'en', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const getNoteColor = (index: number) => {
    const colors = [
      'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20',
      'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20',
      'from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20',
      'from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20',
      'from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20',
      'from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20',
    ];
    return colors[index % colors.length];
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-48 bg-muted rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('notes.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('notes.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('notes.newNote')}
        </button>
      </div>

      {/* Search and Filter */}
      <div className="space-y-4">
        <div className={cn(
          "flex items-center gap-4",
          isRTL && "flex-row-reverse"
        )}>
          <div className="flex-1 relative">
            <Search className={cn(
              "absolute top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5",
              isRTL ? "right-3" : "left-3"
            )} />
            <input
              type="text"
              placeholder={t('common.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={cn(
                "w-full bg-card border border-border rounded-lg py-3 px-12 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                isRTL ? "text-right pr-12" : "text-left pl-12"
              )}
            />
          </div>
          <button
            onClick={() => setShowArchived(!showArchived)}
            className={cn(
              "p-3 border border-border rounded-lg transition-colors",
              showArchived ? "bg-primary text-primary-foreground" : "bg-card hover:bg-accent"
            )}
          >
            <Archive className="w-5 h-5" />
          </button>
          <button className="p-3 bg-card hover:bg-accent border border-border rounded-lg transition-colors">
            <Filter className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Notes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredNotes.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <StickyNote className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">{t('notes.noNotes')}</h3>
            <p className="text-muted-foreground">{t('notes.noNotesDescription')}</p>
          </div>
        ) : (
          filteredNotes.map((note, index) => (
            <div
              key={note.id}
              className={cn(
                `bg-gradient-to-br ${note.color || getNoteColor(index)} border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group relative`
              )}
            >
              {/* Pin button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleTogglePin(note.id);
                }}
                disabled={togglePinMutation.isPending}
                className={cn(
                  "absolute top-4 right-4 p-1 rounded-full transition-colors",
                  note.isPinned
                    ? "text-yellow-500 hover:text-yellow-600"
                    : "text-muted-foreground hover:text-foreground opacity-0 group-hover:opacity-100",
                  togglePinMutation.isPending && "opacity-50 cursor-not-allowed"
                )}
              >
                <Pin className="w-4 h-4" />
              </button>

              <div className="mb-4">
                <h3 className={cn(
                  "text-lg font-semibold mb-2 text-foreground group-hover:text-primary transition-colors pr-8",
                  isRTL && "text-right pl-8 pr-0"
                )}>
                  {note.title}
                </h3>
                <p className={cn(
                  "text-muted-foreground text-sm line-clamp-3",
                  isRTL && "text-right"
                )}>
                  {note.content.length > 100 ? `${note.content.substring(0, 100)}...` : note.content}
                </p>
              </div>

              <div className={cn(
                "flex items-center justify-between text-xs text-muted-foreground mb-3",
                isRTL && "flex-row-reverse"
              )}>
                <span>{t('notes.lastModified')}: {formatDate(note.updatedAt)}</span>
                <span>{note.wordCount} {t('notes.wordCount')}</span>
              </div>

              {note.tags.length > 0 && (
                <div className={cn(
                  "flex gap-1 flex-wrap",
                  isRTL && "flex-row-reverse"
                )}>
                  {note.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="px-2 py-1 bg-white/60 dark:bg-black/20 text-foreground rounded text-xs font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default NotesGrid;
