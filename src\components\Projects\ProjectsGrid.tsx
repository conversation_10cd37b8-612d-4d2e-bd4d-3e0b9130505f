
import React, { useState, useMemo } from 'react';
import { Calendar, Users, TrendingUp, Plus, FolderOpen } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useProjects, useTasksByProject } from '@/hooks/useData';
import type { ProjectFilter } from '@/types/database';

const ProjectsGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { projectStatus } = useColors();
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Build filter object for the query
  const filter: ProjectFilter = useMemo(() => {
    const baseFilter: ProjectFilter = {};

    if (statusFilter !== 'all') {
      baseFilter.status = [statusFilter as any];
    }

    return baseFilter;
  }, [statusFilter]);

  const { data: projectsResult, isLoading, error } = useProjects(filter, {
    page: 1,
    limit: 50,
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  });

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat(isRTL ? 'ar' : 'en', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const getProjectColor = (index: number) => {
    const colors = [
      'from-blue-500 to-blue-600',
      'from-green-500 to-green-600',
      'from-purple-500 to-purple-600',
      'from-orange-500 to-orange-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
    ];
    return colors[index % colors.length];
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-64 bg-muted rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  const projects = projectsResult?.data || [];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('projects.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('projects.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('projects.newProject')}
        </button>
      </div>

      {/* Status Filters */}
      <div className="flex gap-2">
        {['all', 'planning', 'active', 'completed', 'onHold'].map((status) => (
          <button
            key={status}
            onClick={() => setStatusFilter(status)}
            className={cn(
              "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
              statusFilter === status
                ? "bg-zenith-gradient text-white shadow-zenith"
                : "bg-card hover:bg-accent text-foreground border border-border"
            )}
          >
            {t(`projects.status.${status}`) || status}
          </button>
        ))}
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <FolderOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">{t('projects.noProjects')}</h3>
            <p className="text-muted-foreground">{t('projects.noProjectsDescription')}</p>
          </div>
        ) : (
          projects.map((project, index) => (
            <ProjectCard
              key={project.id}
              project={project}
              color={project.color || getProjectColor(index)}
              isRTL={isRTL}
              t={t}
              projectStatus={projectStatus}
              formatDate={formatDate}
            />
          ))
        )}
      </div>
    </div>
  );
};

// Separate component for project card to keep the main component clean
const ProjectCard: React.FC<{
  project: any;
  color: string;
  isRTL: boolean;
  t: (key: string) => string;
  projectStatus: any;
  formatDate: (date: Date | undefined) => string;
}> = ({ project, color, isRTL, t, projectStatus, formatDate }) => {
  const { data: tasks = [] } = useTasksByProject(project.id);
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(task => task.status === 'completed').length;

  return (
    <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 group">
      {/* Project Header */}
      <div className="mb-4">
        <div className={cn(
          "flex items-start justify-between mb-3",
          isRTL && "flex-row-reverse"
        )}>
          <div className={cn(
            `w-3 h-3 rounded-full bg-gradient-to-r ${color}`
          )}></div>
          <span className={cn(
            "text-xs px-2 py-1 rounded-full font-medium",
            project.status === 'active'
              ? projectStatus.active.className
              : project.status === 'completed'
              ? projectStatus.completed.className
              : project.status === 'planning'
              ? projectStatus.pending.className
              : projectStatus.pending.className
          )}>
            {t(`projects.status.${project.status}`)}
          </span>
        </div>

        <h3 className={cn(
          "text-lg font-semibold mb-2 group-hover:text-primary transition-colors",
          isRTL && "text-right"
        )}>
          {project.name}
        </h3>

        {project.description && (
          <p className={cn(
            "text-muted-foreground text-sm",
            isRTL && "text-right"
          )}>
            {project.description}
          </p>
        )}
      </div>

      {/* Progress */}
      <div className="mb-4">
        <div className={cn(
          "flex items-center justify-between text-sm mb-2",
          isRTL && "flex-row-reverse"
        )}>
          <span className="text-muted-foreground">{t('projects.progress')}</span>
          <span className="font-medium">{project.progress}%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className={cn(
              `h-full rounded-full bg-gradient-to-r ${color} transition-all duration-500`
            )}
            style={{ width: `${project.progress}%` }}
          ></div>
        </div>
      </div>

      {/* Stats */}
      <div className={cn(
        "flex items-center justify-between text-sm text-muted-foreground",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(
          "flex items-center gap-1",
          isRTL && "flex-row-reverse"
        )}>
          <Users className="w-4 h-4" />
          <span>{project.teamMembers?.length || 0} {t('projects.members')}</span>
        </div>

        <div className={cn(
          "flex items-center gap-1",
          isRTL && "flex-row-reverse"
        )}>
          <TrendingUp className="w-4 h-4" />
          <span>{totalTasks} {t('projects.tasks')}</span>
        </div>

        {project.deadline && (
          <div className={cn(
            "flex items-center gap-1",
            isRTL && "flex-row-reverse"
          )}>
            <Calendar className="w-4 h-4" />
            <span>{formatDate(project.deadline)}</span>
          </div>
        )}
      </div>
    </div>
  );
};
};

export default ProjectsGrid;
